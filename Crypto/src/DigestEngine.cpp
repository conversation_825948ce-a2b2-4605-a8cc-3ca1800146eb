//
// DigestEngine.cpp
//
// Library: Crypto
// Package: Digest
// Module:  DigestEngine
//
// Copyright (c) 2012, Applied Informatics Software Engineering GmbH.
// and Contributors.
//
// SPDX-License-Identifier:	BSL-1.0
//


#include "Poco/Crypto/DigestEngine.h"
#include "Poco/Exception.h"


namespace Poco {
namespace Crypto {


DigestEngine::DigestEngine(const std::string& name):
	_name(name),
	_pContext(EVP_MD_CTX_create())
{
	const EVP_MD* md = EVP_get_digestbyname(_name.c_str());
	if (!md) throw Poco::NotFoundException(_name);
	EVP_DigestInit_ex(_pContext, md, NULL);
}


DigestEngine::~DigestEngine()
{
	EVP_MD_CTX_destroy(_pContext);
}

int DigestEngine::nid() const
{
#if OPENSSL_VERSION_NUMBER >= 0x30000000L
	return EVP_MD_nid(EVP_MD_CTX_get0_md(_pContext));
#else
	return EVP_MD_nid(EVP_MD_CTX_md(_pContext));
#endif
}

std::size_t DigestEngine::digestLength() const
{
	return EVP_MD_CTX_size(_pContext);
}


void DigestEngine::reset()
{
#if OPENSSL_VERSION_NUMBER >= 0x10100000L
	EVP_MD_CTX_free(_pContext);
	_pContext = EVP_MD_CTX_create();
#else
	EVP_MD_CTX_cleanup(_pContext);
#endif
	const EVP_MD* md = EVP_get_digestbyname(_name.c_str());
	if (!md) throw Poco::NotFoundException(_name);
	EVP_DigestInit_ex(_pContext, md, NULL);
}


const Poco::DigestEngine::Digest& DigestEngine::digest()
{
	_digest.clear();
	unsigned len = EVP_MD_CTX_size(_pContext);
	_digest.resize(len);
	EVP_DigestFinal_ex(_pContext, &_digest[0], &len);
	reset();
	return _digest;
}


void DigestEngine::updateImpl(const void* data, std::size_t length)
{
	EVP_DigestUpdate(_pContext, data, length);
}


} } // namespace Poco::Crypto
