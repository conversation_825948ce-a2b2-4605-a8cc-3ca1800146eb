<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	Name="SQLToMongo"
	Version="9.00"
	ProjectType="Visual C++"
	ProjectGUID="{638D0833-8E84-3A67-BD00-4611F99E65AF}"
	RootNamespace="SQLToMongo"
	Keyword="Win32Proj">
	<Platforms>
		<Platform
			Name="Win32"/>
	</Platforms>
	<ToolFiles/>
	<Configurations>
		<Configuration
			Name="debug_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			CharacterSet="2">
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories=".\include;..\..\..\Foundation\include;..\..\..\Net\include;..\..\..\MongoDB\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;WINVER=0x0500;"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
				AdditionalOptions=""/>
			<Tool
				Name="VCManagedResourceCompilerTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="ws2_32.lib iphlpapi.lib"
				OutputFile="bin\SQLToMongod.exe"
				LinkIncremental="2"
				AdditionalLibraryDirectories="..\..\..\lib"
				SuppressStartupBanner="true"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="bin\SQLToMongod.pdb"
				SubSystem="1"
				TargetMachine="1"
				AdditionalOptions=""/>
			<Tool
				Name="VCALinkTool"/>
			<Tool
				Name="VCManifestTool"/>
			<Tool
				Name="VCXDCMakeTool"/>
			<Tool
				Name="VCBscMakeTool"/>
			<Tool
				Name="VCFxCopTool"/>
			<Tool
				Name="VCAppVerifierTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
		</Configuration>
		<Configuration
			Name="release_shared|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			CharacterSet="2">
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\..\..\Foundation\include;..\..\..\Net\include;..\..\..\MongoDB\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;WINVER=0x0500;"
				StringPooling="true"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
				AdditionalOptions=""/>
			<Tool
				Name="VCManagedResourceCompilerTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="ws2_32.lib iphlpapi.lib"
				OutputFile="bin\SQLToMongo.exe"
				LinkIncremental="1"
				AdditionalLibraryDirectories="..\..\..\lib"
				GenerateDebugInformation="false"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				TargetMachine="1"
				AdditionalOptions=""/>
			<Tool
				Name="VCALinkTool"/>
			<Tool
				Name="VCManifestTool"/>
			<Tool
				Name="VCXDCMakeTool"/>
			<Tool
				Name="VCBscMakeTool"/>
			<Tool
				Name="VCFxCopTool"/>
			<Tool
				Name="VCAppVerifierTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
		</Configuration>
		<Configuration
			Name="debug_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			CharacterSet="2">
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="4"
				AdditionalIncludeDirectories=".\include;..\..\..\Foundation\include;..\..\..\Net\include;..\..\..\MongoDB\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;WINVER=0x0500;POCO_STATIC;"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
				AdditionalOptions=""/>
			<Tool
				Name="VCManagedResourceCompilerTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="iphlpapi.lib winmm.lib ws2_32.lib iphlpapi.lib"
				OutputFile="bin\static_mt\SQLToMongod.exe"
				LinkIncremental="2"
				AdditionalLibraryDirectories="..\..\..\lib"
				SuppressStartupBanner="true"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="bin\static_mt\SQLToMongod.pdb"
				SubSystem="1"
				TargetMachine="1"
				AdditionalOptions=""/>
			<Tool
				Name="VCALinkTool"/>
			<Tool
				Name="VCManifestTool"/>
			<Tool
				Name="VCXDCMakeTool"/>
			<Tool
				Name="VCBscMakeTool"/>
			<Tool
				Name="VCFxCopTool"/>
			<Tool
				Name="VCAppVerifierTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
		</Configuration>
		<Configuration
			Name="release_static_mt|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			CharacterSet="2">
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\..\..\Foundation\include;..\..\..\Net\include;..\..\..\MongoDB\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;WINVER=0x0500;POCO_STATIC;"
				StringPooling="true"
				RuntimeLibrary="0"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
				AdditionalOptions=""/>
			<Tool
				Name="VCManagedResourceCompilerTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="iphlpapi.lib winmm.lib ws2_32.lib iphlpapi.lib"
				OutputFile="bin\static_mt\SQLToMongo.exe"
				LinkIncremental="1"
				AdditionalLibraryDirectories="..\..\..\lib"
				GenerateDebugInformation="false"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				TargetMachine="1"
				AdditionalOptions=""/>
			<Tool
				Name="VCALinkTool"/>
			<Tool
				Name="VCManifestTool"/>
			<Tool
				Name="VCXDCMakeTool"/>
			<Tool
				Name="VCBscMakeTool"/>
			<Tool
				Name="VCFxCopTool"/>
			<Tool
				Name="VCAppVerifierTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
		</Configuration>
		<Configuration
			Name="debug_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			CharacterSet="2">
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="4"
				AdditionalIncludeDirectories=".\include;..\..\..\Foundation\include;..\..\..\Net\include;..\..\..\MongoDB\include"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;WINVER=0x0500;POCO_STATIC;"
				StringPooling="true"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				BufferSecurityCheck="true"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
				CompileAs="0"
				DisableSpecificWarnings=""
				AdditionalOptions=""/>
			<Tool
				Name="VCManagedResourceCompilerTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="iphlpapi.lib winmm.lib ws2_32.lib iphlpapi.lib"
				OutputFile="bin\static_md\SQLToMongod.exe"
				LinkIncremental="2"
				AdditionalLibraryDirectories="..\..\..\lib"
				SuppressStartupBanner="true"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="bin\static_md\SQLToMongod.pdb"
				SubSystem="1"
				TargetMachine="1"
				AdditionalOptions=""/>
			<Tool
				Name="VCALinkTool"/>
			<Tool
				Name="VCManifestTool"/>
			<Tool
				Name="VCXDCMakeTool"/>
			<Tool
				Name="VCBscMakeTool"/>
			<Tool
				Name="VCFxCopTool"/>
			<Tool
				Name="VCAppVerifierTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
		</Configuration>
		<Configuration
			Name="release_static_md|Win32"
			OutputDirectory="obj\$(ConfigurationName)"
			IntermediateDirectory="obj\$(ConfigurationName)"
			ConfigurationType="1"
			CharacterSet="2">
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCXMLDataGeneratorTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="4"
				InlineFunctionExpansion="1"
				EnableIntrinsicFunctions="true"
				FavorSizeOrSpeed="1"
				OmitFramePointers="true"
				AdditionalIncludeDirectories=".\include;..\..\..\Foundation\include;..\..\..\Net\include;..\..\..\MongoDB\include"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;WINVER=0x0500;POCO_STATIC;"
				StringPooling="true"
				RuntimeLibrary="2"
				BufferSecurityCheck="false"
				TreatWChar_tAsBuiltInType="true"
				ForceConformanceInForLoopScope="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="0"
				CompileAs="0"
				DisableSpecificWarnings=""
				AdditionalOptions=""/>
			<Tool
				Name="VCManagedResourceCompilerTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="iphlpapi.lib winmm.lib ws2_32.lib iphlpapi.lib"
				OutputFile="bin\static_md\SQLToMongo.exe"
				LinkIncremental="1"
				AdditionalLibraryDirectories="..\..\..\lib"
				GenerateDebugInformation="false"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				TargetMachine="1"
				AdditionalOptions=""/>
			<Tool
				Name="VCALinkTool"/>
			<Tool
				Name="VCManifestTool"/>
			<Tool
				Name="VCXDCMakeTool"/>
			<Tool
				Name="VCBscMakeTool"/>
			<Tool
				Name="VCFxCopTool"/>
			<Tool
				Name="VCAppVerifierTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
		</Configuration>
	</Configurations>
	<References/>
	<Files>
		<Filter
			Name="Header Files"/>
		<Filter
			Name="Source Files">
			<File
				RelativePath=".\src\SQLToMongo.cpp"/>
		</Filter>
	</Files>
	<Globals/>
</VisualStudioProject>
